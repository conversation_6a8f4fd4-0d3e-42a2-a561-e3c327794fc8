package lessonList

type LessonRuleConfigStru struct {
	Key        string
	Lable      string
	Function   string
	Sort       int
	FusingRule FusingRuleStru
}
type FusingRuleStru struct {
	TimeoutWarning int64 `json:"timeoutWarning"` //熔断报警墙 单位：ms
	TimeoutFusing  int64 `json:"timeoutFusing"`  //熔断墙 单位：ms
	FusingDuration int64 `json:"fusingDuration"` //熔断时长 单位：s
	Duration       int64 `json:"duration"`       //持续时间 单位：s
	DurationTimes  int64 `json:"durationTimes"`  //持续次数
}

var RuleList []*LessonRuleConfigStru = []*LessonRuleConfigStru{
	&LessonRuleConfigStru{
		Key:      "lessonId",
		Function: "GetLessonId",
		Lable:    "获取章节ID",
	},
}

func GetRuleMap() map[string]*LessonRuleConfigStru {
	ruleMap := map[string]*LessonRuleConfigStru{}
	for idx := range RuleList {
		ruleDetail := RuleList[idx]
		ruleMap[ruleDetail.Key] = ruleDetail
	}
	return ruleMap
}
