tower:
  service: tower
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://tower-svc.support:8080
  timeout: 1000ms
  httpStat: true
  retry: 1

jxnotice:
  service: jxnotice
  # 请求完整地址
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://jxnotice-svc.edu:8080
  timeout: 1500ms
  retry: 1

livestation:
  service: livestation
  # 请求完整地址
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://livestation-svc.edu:8080
  timeout: 1000ms
  retry: 1

assistantdesk:
  service: assistantdesk
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://assistantdesk-svc.support:8080
  timeout: 6000ms
  retry: 1

moat:
  service: moat
  domain: http://127.0.0.1:8010/fwyytool/tools/httptest/router?domain=http://moat2-svc.sell-gateway:8080
  timeout: 1000ms
  retry: 1

dataproxy:
  service: dataproxy
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://dataproxy-svc.support:8080
  timeout: 1000ms
  retry: 1

zbcoredal:
  service: zbcoredal
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://dal-svc.sell-course:8080
  timeout: 1500ms
  httpStat: true
  retry: 1

zbcoredat:
  service: zbcoredat
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://dat-svc.sell-course:8080
  timeout: 1500ms
  httpStat: true
  retry: 1

zbcoredau:
  service: zbcoredau
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://dau-svc.sell-course:8080
  timeout: 1500ms
  httpStat: true
  retry: 1

zbcoredas:
  service: zbcoredas
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://das-svc.sell-course:8080
  timeout: 1500ms
  httpStat: true
  retry: 1

zbcoredar:
  service: zbcoredar
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://moat2-svc.sell-gateway:8080
  timeout: 1500ms
  httpStat: true
  retry: 1
  moatAppKey: assistantdesk
  moatAppSecret: 86ec2d3211ac6e7ff5fcbd95fbf4e2c0

longservice:
  service: longservice
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://10.116.252.48:8090
  timeout: 1000ms
  httpStat: true
  retry: 1

mercury:
  service: mercury
  domain: http://mercury-svc.edu:8080
  timeout: 3000ms
  retry: 1

mesh:
  service: mesh
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://fwyy-mesh-svc.support:8080
  appkey: 68DA5D3D7F2859B0
  timeout: 1500ms
  retry: 1

allocate:
  service: allocate
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://allocate-svc.lpc:8080
  timeout: 1000ms
  httpStat: true
  retry: 1

assistantdeskgo:
  service: assistantdeskgo
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://assistantdeskgo-svc.support:8080
  timeout: 3000ms
  retry: 3

arkgo:
  service: arkgo
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://arkgo-svc.support:8080
  timeout: 3000ms
  retry: 3

genke:
  service: genke
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://genke-svc.support:8080
  timeout: 3000ms
  retry: 3

kpstaff:
  service: kpstaff
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://kpstaff-svc.kunpeng:8080
  timeout: 2000ms
  retry: 2

coursetransgo:
  service: coursetransgo
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://coursetransgo-svc.lpc:8080
  timeout: 2000ms
  retry: 3

jxreport:
  service: jxreport
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://jxreport-svc.edu:8080
  timeout: 3000ms
  retry: 1

jwbiz:
  service: jwbiz
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://jwbiz-svc.sell-course:8080
  timeout: 3000ms
  retry: 1

userprofile:
  service: userprofile
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://userprofile-svc.support:8080
  timeout: 3000ms
  retry: 2

examcore:
  service: examcore
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://examcore-svc.edu:8080
  timeout: 3000ms
  retry: 1

dalgo:
  service: dalgo
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://dalgo-svc.sell-course:8080
  timeout: 2500ms
  httpStat: true
  retry: 1

kpapi:
  service: kpapi
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://kpapi-svc.kunpeng:8080
  timeout: 2000ms
  retry: 1

lpcduxuesc:
  service: lpcduxuesc
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://duxuesc-svc.lpc:8080
  timeout: 5000ms
  retry: 1

learningplan:
  service: learningplan
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://learnplan-go-svc.edu:8080
  timeout: 3000ms
  httpStat: true
  retry: 1

achilles-v3:
  service: achilles-v3
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://achilles-v3-server-svc.edu:8080
  timeout: 3000ms
  httpStat: true
  retry: 1

tag:
  service: tag
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://tag-svc.support-xieyi:8080
  timeout: 3000ms
  retry: 1

muse:
  service: muse
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://muse-svc.support:8080
  timeout: 3000ms
  retry: 1

touchmisgo:
  service: touchmisgo
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://touchmisgo-svc.support:8080
  timeout: 3000ms
  retry: 1

touchmis:
  service: touchmis
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://touchmis-svc.lpc:8080
  timeout: 3000ms
  retry: 1

location:
  service: location
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://location-svc.inf:8080
  timeout: 3000ms
  retry: 1

frontcoursenew:
  service: frontcoursenew
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://frontcourse-svc.edu-front:8080
  timeout: 3000ms
  retry: 1

jxexamui:
  service: jxexamui
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://jxexamui-svc.edu-front:8080
  timeout: 3000ms
  retry: 1

pcassistant:
  service: pcassistant
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://pcassistant-svc.support:8080
  timeout: 3000ms
  retry: 1

jxdascore:
  service: jxdascore
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://jxda-score-svc.edu:8080
  timeout: 3000ms
  retry: 1

writereport:
  service: writereport
  domain: https://assistantdesk.zuoyebang.cc/fwyytool/tools/httptest/router?domain=http://writereport-svc.deer:8080
  timeout: 3000ms
  retry: 1

# 本地deskcrm服务API客户端
deskcrm:
  service: deskcrm
  domain: http://127.0.0.1:8010
  timeout: 3000ms
  retry: 1